# Default values for auth-serverv2.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
hpa: true
maxReplicas: 20
targetCPUUtilization: 80
pdb: true
minAvailable: 1
image:
  auth-serverv2:
    repository: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com/auth_v2-server
    tag: 02f626ee16d8612c6a5c920c6cfcca839bf4823e
    pullPolicy: Always
nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  className: ""
  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  path: /
  hosts:
    - chart-example.local
  tls: []
resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 20m
    memory: 32Mi
nodeSelector: {}
tolerations: []
affinity: {}
auth-serverv2:
  database:
    username: ""
    password: ""
    host: ""
    port: ""
    name: ""
env: ""
