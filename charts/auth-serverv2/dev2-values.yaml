replicaCount: 1
auth-serverv2:
  quarkus_profile: "dev2"

env: dev2

ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/group.name: public-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:638984414044:certificate/1906982a-7a99-4655-93a3-79c2e6d293eb"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: '8080'
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '10'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /hc
    alb.ingress.kubernetes.io/success-codes: '200'
  path: /*
  hosts:
    - auth.dev2.fanme.link
  servicePort: 80

services:
  - name: authv2-server-svc
    domains:
      - authv2:80
    forward:
      address: authv2.svc.cluster.local
      port: 8080
    http2: true
    internalService: true
    tls: true
    healthcheck: true

hpa: false
resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi
