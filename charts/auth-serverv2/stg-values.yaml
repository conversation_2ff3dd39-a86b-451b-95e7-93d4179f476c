auth-serverv2:
  quarkus_profile: "stg"

env: stg

auto-scale: false

ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/group.name: public-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:638984414044:certificate/beb7368c-538a-408d-9d02-da1876ee0046, arn:aws:acm:ap-northeast-1:638984414044:certificate/a91391c4-1f83-486a-95e4-1d902a2a1a44"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: '8080'
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '10'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /hc
    alb.ingress.kubernetes.io/success-codes: '200'
  path: /*
  hosts:
    - auth.stg.fanme.link
  servicePort: 80

services:
  - name: authv2-server-svc
    domains:
      - authv2:80
    forward:
      address: authv2.stg.svc.cluster.local
      port: 8080
    http2: true
    internalService: true
    tls: true
    healthcheck: true

resources:
  limits:
    cpu: 2000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi
