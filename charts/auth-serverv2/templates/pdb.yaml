{{- if .Values.pdb -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "auth-serverv2.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
    helm.sh/chart: {{ include "auth-serverv2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  minAvailable: {{ .Values.minAvailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}
