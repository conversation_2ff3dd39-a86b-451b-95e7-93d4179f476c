apiVersion: v1
kind: Service
metadata:
  name: {{ include "auth-serverv2.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
    helm.sh/chart: {{ include "auth-serverv2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: ClusterIP
  clusterIP: "None"
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
