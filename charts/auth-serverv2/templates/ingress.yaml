{{- if .Values.ingress.enabled -}}
{{- $fullName := include "auth-serverv2.fullname" . -}}
{{- $ingressPath := .Values.ingress.path -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
    helm.sh/chart: {{ include "auth-serverv2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.ingress.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  ingressClassName: {{ .Values.ingress.className }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ . | quote }}
      http:
        paths:
          - path: {{ $ingressPath }}
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ include "auth-serverv2.fullname" $ }}
                port:
                  number: 80
  {{- end }}
{{- end }}
