apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "auth-serverv2.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
    helm.sh/chart: {{ include "auth-serverv2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: {{ index .Values "auto-scale" | quote }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ index .Values "image" "auth-serverv2" "repository" }}:{{ index .Values "image" "auth-serverv2" "tag" }}"
          imagePullPolicy: {{ index .Values "image" "auth-serverv2" "pullPolicy" }}
          command:
            - /opt/jboss/container/java/run/run-java.sh
          resources:
{{- toYaml (index .Values "resources") | nindent 12 }}
          env:
            - name: QUARKUS_PROFILE
              value: {{ index .Values "auth-serverv2" "quarkus_profile" | quote }}
          lifecycle:
            preStop:
              exec:
                command:
                  - sleep
                  - "40"
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ include "auth-serverv2.fullname" . }}
