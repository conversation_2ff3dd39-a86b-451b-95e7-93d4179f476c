{{- if .Values.hpa -}}
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "auth-serverv2.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "auth-serverv2.name" . }}
    helm.sh/chart: {{ include "auth-serverv2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "auth-serverv2.fullname" . }}
  minReplicas: {{ .Values.replicaCount }}
  maxReplicas: {{ .Values.maxReplicas }}
  targetCPUUtilizationPercentage: {{ .Values.targetCPUUtilization }}
{{- end -}}
