replicaCount: 2

fanme-admin:
  database:
    payment_db_url: "**************************************************************************************"
    payment_db_name: "payment"
    auth_db_url: "**************************************************************************************"
    auth_db_name: "auth"
    fanme_api_db_url: "**************************************************************************************"
    fanme_api_db_name: "fanme"
  tenant: "fanme"
  ktor_env: "production"
  server_host: "https://admin.fanme.link"
  miniapp_host: "https://app.fanme.link"
  jwk_provider: "https://auth.fanme.link/oauth2/certs"
  jwt_issuer: "https://auth.fanme.link"
  miniapp_bucket: "m-a-prd"
  miniapp_blob_bucket: "miniapp-blob-prd"
  email_from: "<EMAIL>"
  email_smtp_host: "email-smtp.ap-northeast-1.amazonaws.com"
  email_smtp_port: "2587"
  email_smtp_user: "AKIAZJRTQMNOLYBEJVXK"
  email_smtp_pass: "BG3zxgSQyLRgpvgfec7AVBXYNbxt2OeAXbriVAkDN8/E"


ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/group.name: public-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:638984414044:certificate/beb7368c-538a-408d-9d02-da1876ee0046"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: '8080'
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '10'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /hc
    alb.ingress.kubernetes.io/success-codes: '200'
  path: /*
  hosts:
    - adm.fanme.link
  servicePort: 80

env: prd

services:
  - name: fanme-admin-svc
    domains:
      - admin:80
    forward:
      address: admin.svc.cluster.local
      port: 8080
    http2: true
    internalService: true
    tls: true
    healthcheck: true

resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi
