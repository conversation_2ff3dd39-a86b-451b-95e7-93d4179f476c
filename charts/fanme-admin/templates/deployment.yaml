apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fanme-admin.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
    helm.sh/chart: {{ include "fanme-admin.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ index .Values "image" "fanme-admin" "repository" }}:{{ index .Values "image" "fanme-admin" "tag" }}"
          imagePullPolicy: {{ index .Values "image" "fanme-admin" "pullPolicy" }}
          workingDir: /app
          command:
            - java
          args:
            - -jar
            - /app/build/libs/fanme-admin.jar
          resources:
{{- toYaml (index .Values "resources") | nindent 12 }}
          env:
            - name: KTOR_ENV
              value: {{ index .Values "fanme-admin" "ktor_env" | quote }}
            - name: PAYMENT_DB_URL
              value: {{ index .Values "fanme-admin" "database" "payment_db_url" | quote }}
            - name: PAYMENT_DB_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.payment_db_user
            - name: PAYMENT_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.payment_db_pass
            - name: PAYMENT_DB_NAME
              value: {{ index .Values "fanme-admin" "database" "payment_db_name" | quote }}
            - name: AUTH_DB_URL
              value: {{ index .Values "fanme-admin" "database" "auth_db_url" | quote }}
            - name: AUTH_DB_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.auth_db_user
            - name: AUTH_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.auth_db_pass
            - name: AUTH_DB_NAME
              value: {{ index .Values "fanme-admin" "database" "auth_db_name" | quote }}
            - name: FANME_API_DB_URL
              value: {{ index .Values "fanme-admin" "database" "fanme_api_db_url" | quote }}
            - name: FANME_API_DB_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.fanme_api_db_user
            - name: FANME_API_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.fanme_api_db_pass
            - name: FANME_API_DB_NAME
              value: {{ index .Values "fanme-admin" "database" "fanme_api_db_name" | quote }}
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: aws.aws_access_key
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: aws.aws_secret_key
            - name: MONGO_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: database.mongo_url
            - name: TENANT
              value: {{ index .Values "fanme-admin" "tenant" | quote }}
            - name: PAYMENT_HOST
              value: {{ index .Values "fanme-admin" "server_host" | quote }}
            - name: MINIAPP_HOST
              value: {{ index .Values "fanme-admin" "miniapp_host" | quote }}
            - name: JWK_PROVIDER
              value: {{ index .Values "fanme-admin" "jwk_provider" | quote }}
            - name: JWT_ISSUER
              value: {{ index .Values "fanme-admin" "jwt_issuer" | quote }}
            - name: MINIAPP_BUCKET
              value: {{ index .Values "fanme-admin" "miniapp_bucket" | quote }}
            - name: MINIAPP_BLOB_BUCKET
              value: {{ index .Values "fanme-admin" "miniapp_blob_bucket" | quote }}
            - name: PRIVATE_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: keys.private_key
            - name: PUBLIC_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: keys.public_key
            - name: GMO_SITE_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: gmo.gmo_site_id
            - name: GMO_SITE_PASS
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: gmo.gmo_site_pass
            - name: GMO_SHOP_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: gmo.gmo_shop_id
            - name: GMO_SHOP_PASS
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: gmo.gmo_shop_pass
            - name: EMAIL_FROM
              value: {{ index .Values "fanme-admin" "email_from" | quote }}
            - name: EMAIL_SMTP_HOST
              value: {{ index .Values "fanme-admin" "email_smtp_host" | quote }}
            - name: EMAIL_SMTP_PORT
              value: {{ index .Values "fanme-admin" "email_smtp_port" | quote }}
            - name: EMAIL_SMTP_USER
              value: {{ index .Values "fanme-admin" "email_smtp_user" | quote }}
            - name: EMAIL_SMTP_PASS
              value: {{ index .Values "fanme-admin" "email_smtp_pass" | quote }}
            - name: GOOGLE_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: google_oauth.client_id
            - name: GOOGLE_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: google_oauth.client_secret
            - name: GOOGLE_REDIRECT_URI
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-admin.fullname" . }}
                  key: google_oauth.redirect_uri
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
