apiVersion: v1
kind: Secret
metadata:
  name: {{ include "fanme-admin.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
    helm.sh/chart: {{ include "fanme-admin.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
type: Opaque
data:
  database.payment_db_user: {{ index .Values "fanme-admin" "database" "payment_db_user" | b64enc | quote }}
  database.payment_db_pass: {{ index .Values "fanme-admin" "database" "payment_db_pass" | b64enc | quote }}
  database.auth_db_user: {{ index .Values "fanme-admin" "database" "auth_db_user" | b64enc | quote }}
  database.auth_db_pass: {{ index .Values "fanme-admin" "database" "auth_db_pass" | b64enc | quote }}
  database.fanme_api_db_user: {{ index .Values "fanme-admin" "database" "fanme_api_db_user" | b64enc | quote }}
  database.fanme_api_db_pass: {{ index .Values "fanme-admin" "database" "fanme_api_db_pass" | b64enc | quote }}
  database.mongo_url: {{ index .Values "fanme-admin" "database" "mongo_url" | b64enc | quote }}
  aws.aws_access_key: {{ index .Values "fanme-admin" "aws" "aws_access_key" | b64enc | quote }}
  aws.aws_secret_key: {{ index .Values "fanme-admin" "aws" "aws_secret_key" | b64enc | quote }}
  keys.private_key: {{ index .Values "fanme-admin" "keys" "private_key" | b64enc | quote }}
  keys.public_key: {{ index .Values "fanme-admin" "keys" "public_key" | b64enc | quote }}
  gmo.gmo_site_id: {{ index .Values "fanme-admin" "gmo" "gmo_site_id" | b64enc | quote }}
  gmo.gmo_site_pass: {{ index .Values "fanme-admin" "gmo" "gmo_site_pass" | b64enc | quote }}
  gmo.gmo_shop_id: {{ index .Values "fanme-admin" "gmo" "gmo_shop_id" | b64enc | quote }}
  gmo.gmo_shop_pass: {{ index .Values "fanme-admin" "gmo" "gmo_shop_pass" | b64enc | quote }}
  google_oauth.client_id: {{ index .Values "fanme-admin" "google_oauth" "client_id" | b64enc | quote }}
  google_oauth.client_secret: {{ index .Values "fanme-admin" "google_oauth" "client_secret" | b64enc | quote }}
  google_oauth.redirect_uri: {{ index .Values "fanme-admin" "google_oauth" "redirect_uri" | b64enc | quote }}
