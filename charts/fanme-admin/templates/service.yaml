apiVersion: v1
kind: Service
metadata:
  name: {{ include "fanme-admin.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
    helm.sh/chart: {{ include "fanme-admin.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: ClusterIP
  clusterIP: "None"
  ports:
    - port: 80
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
