{{- if .Values.pdb -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "fanme-admin.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
    helm.sh/chart: {{ include "fanme-admin.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  minAvailable: {{ .Values.minAvailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "fanme-admin.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}
