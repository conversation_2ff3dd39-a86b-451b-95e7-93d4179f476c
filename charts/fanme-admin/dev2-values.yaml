replicaCount: 1

fanme-admin:
  database:
    payment_db_url: "***************************************************************************************"
    payment_db_name: "payment_dev2"
    auth_db_url: "***************************************************************************************"
    auth_db_name: "auth"
    fanme_api_db_url: "***************************************************************************************"
    fanme_api_db_name: "fanme_dev2"
  tenant: "fanme"
  ktor_env: "dev2"
  server_host: "https://admin.dev2.fanme.link"
  miniapp_host: "https://app.dev2.fanme.link"
  jwk_provider: "https://auth.dev2.fanme.link/oauth2/certs"
  jwt_issuer: "https://auth.dev2.fanme.link"
  miniapp_bucket: "m-a-dev1"
  miniapp_blob_bucket: "miniapp-blob-dev1"
  email_from: "<EMAIL>"
  email_smtp_host: "email-smtp.ap-northeast-1.amazonaws.com"
  email_smtp_port: "2587"
  email_smtp_user: "AKIAZJRTQMNOLYBEJVXK"
  email_smtp_pass: "BG3zxgSQyLRgpvgfec7AVBXYNbxt2OeAXbriVAkDN8/E"


ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/group.name: public-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:638984414044:certificate/1906982a-7a99-4655-93a3-79c2e6d293eb"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: '8080'
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '10'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /hc
    alb.ingress.kubernetes.io/success-codes: '200'
  path: /*
  hosts:
    - adm.dev2.fanme.link
  servicePort: 80

env: dev2

services: 
  - name: fanme-admin-svc
    domains:
      - admin:80
    forward:
      address: admin.svc.cluster.local
      port: 8080
    http2: true
    internalService: true
    tls: true
    healthcheck: true

hpa: false
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
