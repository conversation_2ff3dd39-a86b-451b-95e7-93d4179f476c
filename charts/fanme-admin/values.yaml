# Default values for fanme-admin.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
hpa: true
maxReplicas: 10
targetCPUUtilization: 80
pdb: true
minAvailable: 1
image:
  fanme-admin:
    repository: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com/fanme-admin
    tag: 02f626ee16d8612c6a5c920c6cfcca839bf4823e
    pullPolicy: Always
nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  className: ""
  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  path: /
  hosts:
    - chart-example.local
  tls: []
nodeSelector: {}
tolerations: []
affinity: {}
env: ""
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
