# Default values for fanme-api.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
hpa: true
maxReplicas: 20
targetCPUUtilization: 80
pdb: true
minAvailable: 1
image:
  fanme-api:
    repository: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com/fanme-api
    tag: 02f626ee16d8612c6a5c920c6cfcca839bf4823e
    pullPolicy: Always
aws:
  s3: ""
nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  className: ""
  annotations: {}
  path: /
  hosts:
    - chart-example.local
  tls: []
nodeSelector: {}
tolerations: []
affinity: {}
fanme-api:
  database:
    username: ""
    password: ""
    host: ""
    port: ""
    name: ""
env: ""
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
