apiVersion: v1
kind: Service
metadata:
  name: {{ include "fanme-api.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    helm.sh/chart: {{ include "fanme-api.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  type: ClusterIP
  clusterIP: "None"
  ports:
    - port: 80
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
