{{- if .Values.hpa -}}
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "fanme-api.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    helm.sh/chart: {{ include "fanme-api.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "fanme-api.fullname" . }}
  minReplicas: {{ .Values.replicaCount }}
  maxReplicas: {{ .Values.maxReplicas }}
  targetCPUUtilizationPercentage: {{ .Values.targetCPUUtilization }}
{{- end -}}
