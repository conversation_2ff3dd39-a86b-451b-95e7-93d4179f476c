apiVersion: v1
kind: Secret
metadata:
  name: {{ include "fanme-api.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    helm.sh/chart: {{ include "fanme-api.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
type: Opaque
data:
  database.username: {{ index .Values "fanme-api" "database" "username" | b64enc | quote }}
  database.password: {{ index .Values "fanme-api" "database" "password" | b64enc | quote }}
  aws.aws_access_key: {{ index .Values "fanme-api" "aws" "aws_access_key" | b64enc | quote }}
  aws.aws_secret_key: {{ index .Values "fanme-api" "aws" "aws_secret_key" | b64enc | quote }}
  keys.secret_key_base: {{ index .Values "fanme-api" "keys" "secret_key_base" | b64enc | quote }}
  keys.oauth_client_id: {{ index .Values "fanme-api" "keys" "oauth_client_id" | b64enc | quote }}
  keys.oauth_client_secret: {{ index .Values "fanme-api" "keys" "oauth_client_secret" | b64enc | quote }}
  s2s.payment_api_key: {{ index .Values "fanme-api" "s2s" "payment_api_key" | b64enc | quote }}
  s2s.payment_api_secret: {{ index .Values "fanme-api" "s2s" "payment_api_secret" | b64enc | quote }}
