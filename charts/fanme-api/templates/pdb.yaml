{{- if .Values.pdb -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "fanme-api.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    helm.sh/chart: {{ include "fanme-api.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  minAvailable: {{ .Values.minAvailable }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "fanme-api.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}
