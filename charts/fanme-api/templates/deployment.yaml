apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fanme-api.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    helm.sh/chart: {{ include "fanme-api.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "fanme-api.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "fanme-api.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ index .Values "image" "fanme-api" "repository" }}:{{ index .Values "image" "fanme-api" "tag" }}"
          imagePullPolicy: {{ index .Values "image" "fanme-api" "pullPolicy" }}
          command:
            - rails
          args:
            - server
          resources:
{{- toYaml (index .Values "resources") | nindent 12 }}
          env:
            - name: RAILS_ENV
              value: {{ index .Values "rails" "env" | quote }}
            - name: RAILS_LOG_TO_STDOUT
              value: {{ index .Values "rails" "rails_log_to_stdout" | quote }}
            - name: DATABASE_HOSTNAME
              value: {{ index .Values "fanme-api" "database" "host" | quote }}
            - name: DATABASE_PORT
              value: {{ index .Values "fanme-api" "database" "port" | quote }}
            - name: DATABASE_NAME
              value: {{ index .Values "fanme-api" "database" "database" | quote }}
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: database.username
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: database.password
            - name: AWS_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: aws.aws_access_key
            - name: S3_BUCKET_NAME
              value: {{ index .Values "aws" "s3" | quote }}
            - name: AWS_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: aws.aws_secret_key
            - name: SECRET_KEY_BASE
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: keys.secret_key_base
            - name: OAUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: keys.oauth_client_id
            - name: OAUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: keys.oauth_client_secret
            - name: PAYMENT_SERVER_BASIC_API_NAME
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: s2s.payment_api_key
            - name: PAYMENT_SERVER_BASIC_API_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "fanme-api.fullname" . }}
                  key: s2s.payment_api_secret
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
