{{- if .Values.ingress.enabled -}}
{{- $fullName := include "fanme-api.fullname" . -}}
{{- $ingressPath := .Values.ingress.path -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-api.name" . }}
    helm.sh/chart: {{ include "fanme-api.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.ingress.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  ingressClassName: {{ .Values.ingress.className }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ . | quote }}
      http:
        paths:
          - path: {{ $ingressPath }}
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $fullName }}
                port:
                  number: 80
  {{- end }}
{{- end }}
