replicaCount: 1

fanme-api:
  database:
    host: "fanme-stg-db01.cluster-ccxdhkwejvsb.ap-northeast-1.rds.amazonaws.com"
    port: 3306
    database: "fanme"

aws:
  s3: "fanme-stg-api"

rails:
  env: staging
  rails_log_to_stdout: true

env: stg
ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/group.name: public-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:638984414044:certificate/beb7368c-538a-408d-9d02-da1876ee0046, arn:aws:acm:ap-northeast-1:638984414044:certificate/a91391c4-1f83-486a-95e4-1d902a2a1a44"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: '3000'
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '10'
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /hc
    alb.ingress.kubernetes.io/success-codes: '200'
  path: /*
  hosts:
    - bff.stg.fanme.link
  servicePort: 80

services:
  - name: fanme-api-svc
    domains:
      - bff:80
    forward:
      address: bff.stg.svc.cluster.local
      port: 3000
    http2: true
    internalService: true
    tls: true
    healthcheck: true

hpa: false
resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi