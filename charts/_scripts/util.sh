#!/bin/bash

set -eu

script_dir=$(cd $(dirname $0) && pwd)

usage() {
    echo "Usage: $0 [-a diff|apply] [-e demo|dev|dev2|stg|prd] [-r release_name]" 1>&2
    exit 1
}

while getopts a:e:r: opt
do
    case $opt in
        a)  action=$OPTARG
            ;;
        e)  env=$OPTARG
            ;;
        r)  release_name=$OPTARG
            ;;
        *) usage
            ;;
    esac
done

shift $((OPTIND - 1))

# validate 'action'
if [ "${action}" == "apply" ]; then
    cmd="apply"
    cmdopts="--suppress-diff --suppress-secrets --context=3"
elif [ "${action}" == "diff" ]; then
    cmd="diff"
    cmdopts="--suppress-secrets --context=3"
else
    usage
    exit 1
fi

namespaces=("demo" "dev" "dev2" "stg" "prd")
if ! `echo "${namespaces[@]}" | grep -q "${env}"` ; then
    usage
    exit 1
fi

set -x

helmfile -f "${script_dir}/../helmfile.d" -e ${env} -l name=${release_name} ${cmd} ${cmdopts}
