name: <PERSON><PERSON><PERSON>(developer)
run-name: <PERSON><PERSON><PERSON>(developer) - ${{ github.event.inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: "環境を選択してください"
        required: true
        default: "(select)"
        options:
          - production
          - staging
          - dev1

defaults:
  run:
    working-directory: ./services/developer

permissions:
  id-token: write
  contents: read

env:
  AWS_ROLE_ARN: arn:aws:iam::638984414044:role/github-actions # GitHub Actions OIDC用IAMロール
  GCP_PROJECT: |-
    ${{ fromJson('{
      "dev1": "fanme-dev1",
      "staging": "fanme-stg",
      "production": "fanme-339800"
    }')[github.event.inputs.environment] }}
  SERVICE_NAME: fanme-developer
  KTOR_ENV: |-
    ${{ from<PERSON>son('{
      "dev1": "dev1",
      "staging": "staging",
      "production": "production"
    }')[github.event.inputs.environment] }}
  ARTIFACT_REPOSITORY: docker-main
  SSM_PARAMETER_NAME: |-
    ${{ from<PERSON><PERSON>('{
      "dev1": "PS_DEV1_GOOGLE_CREDENTIALS",
      "staging": "PS_STG_GOOGLE_CREDENTIALS",
      "production": "PS_GOOGLE_CREDENTIALS"
    }')[github.event.inputs.environment] }}

jobs:
  deployment:
    environment:
      name: ${{ github.event.inputs.environment }}

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ap-northeast-1

      - id: install-aws-cli
        uses: unfor19/install-aws-cli-action@v1.0.3
        with:
          version: 2

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'

      - uses: actions/cache@v3
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Build with Gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: wrapper
          arguments: build
          build-root-directory: ./services/developer

      - name: Get google credentials
        run: >
          aws ssm get-parameter --name ${{ env.SSM_PARAMETER_NAME }} --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./google_credentials.json && 
          echo "GOOGLE_CREDENTIALS=$(cat ./google_credentials.json)" >> $GITHUB_ENV

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ env.GOOGLE_CREDENTIALS }}
          create_credentials_file: true
          cleanup_credentials: false

      - name: Setup Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure docker for artifact registry
        run: gcloud auth configure-docker asia-northeast1-docker.pkg.dev

      - name: Generate docker image tag
        run: echo "TAG=$(echo $GITHUB_REF | awk -F/ '{print $NF}')" >> $GITHUB_ENV

      - name: Build docker image
        run: docker build -t asia-northeast1-docker.pkg.dev/${{ env.GCP_PROJECT }}/${{ env.ARTIFACT_REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ env.TAG }} ./

      - name: Push docker image
        run: docker push asia-northeast1-docker.pkg.dev/${{ env.GCP_PROJECT }}/${{ env.ARTIFACT_REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ env.TAG }}

      - name: Deploy to Cloud Run
        run: |-
          gcloud run deploy ${{ env.SERVICE_NAME }} \
            --project=${{ env.GCP_PROJECT }} \
            --image=asia-northeast1-docker.pkg.dev/${{ env.GCP_PROJECT }}/${{ env.ARTIFACT_REPOSITORY }}/${{ env.SERVICE_NAME }}:${{ env.TAG }} \
            --region="asia-northeast1" \
            --allow-unauthenticated \
            --set-env-vars="AWS_ACCESS_KEY_ID=${{ secrets.AWS_ACCESS_KEY_ID }},AWS_SECRET_ACCESS_KEY=${{ secrets.AWS_SECRET_ACCESS_KEY }},KTOR_ENV=${{ env.KTOR_ENV }}" \
            --async \
            --memory 2048M
