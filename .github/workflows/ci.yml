name: TEST

on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
  workflow_dispatch:

jobs:
  check-changes:
    name: 実行するテストの選別
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'workflow_dispatch' || (github.event_name == 'pull_request' && github.event.pull_request.draft == false) }}
    outputs:
      admin: ${{ steps.filter.outputs.admin }}
      frontend: ${{ steps.filter.outputs.frontend }}
      backend: ${{ steps.filter.outputs.backend }}
      auth: ${{ steps.filter.outputs.auth }}
      auth_v2: ${{ steps.filter.outputs.auth_v2 }}
      payment: ${{ steps.filter.outputs.payment }}
      developer: ${{ steps.filter.outputs.developer }}
      fanme_console: ${{ steps.filter.outputs.fanme_console }}
    steps:
      - uses: actions/checkout@v3
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            admin:
              - services/admin/**
            frontend:
              - services/frontend/**
            backend:
              - services/backend/**
            auth:
              - services/auth/**
            auth_v2:
              - services/auth_v2/**
            payment:
              - services/payment/**
            developer:
              - services/developer/**
            fanme_console:
              - services/fanme_console/**

  # Githubブランチプロテクトの「Require status checks to pass」を設定したいため、
  # skipは各処理内で行う
  call-admin-ci:
    name: adminテスト
    needs: check-changes
    uses: ./.github/workflows/ci_admin.yml
    with:
      skip: ${{ needs.check-changes.outputs.admin == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

  call-frontend-ci:
    name: frontendテスト
    needs: check-changes
    uses: ./.github/workflows/ci_frontend.yml
    with:
      skip: ${{ needs.check-changes.outputs.frontend == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
      AUTH_TOKEN_FOR_GITHUBPKG: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

  call-backend-ci:
    name: backendテスト
    needs: check-changes
    uses: ./.github/workflows/ci_backend.yml
    with:
      skip: ${{ needs.check-changes.outputs.backend == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

  call-auth-ci:
    name: authテスト
    needs: check-changes
    uses: ./.github/workflows/ci_auth.yml
    with:
      skip: ${{ needs.check-changes.outputs.auth == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

  call-auth_v2-ci:
    name: auth_v2テスト
    needs: check-changes
    uses: ./.github/workflows/ci_auth_v2.yml
    with:
      skip: ${{ needs.check-changes.outputs.auth_v2 == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

  call-payment-ci:
    name: paymentテスト
    needs: check-changes
    uses: ./.github/workflows/ci_payment.yml
    with:
      skip: ${{ needs.check-changes.outputs.payment == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

  call-developer-ci:
    name: developerテスト
    needs: check-changes
    uses: ./.github/workflows/ci_developer.yml
    with:
      skip: ${{ needs.check-changes.outputs.developer == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

  call-fanme_console-ci:
    name: fanme_consoleテスト
    needs: check-changes
    uses: ./.github/workflows/ci_console.yml
    with:
      skip: ${{ needs.check-changes.outputs.fanme_console == 'true' && 'false' || 'true' }}
    secrets:
      SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
