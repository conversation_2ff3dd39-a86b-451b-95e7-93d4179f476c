name: __auth_v2ビルド

on:
  workflow_call:
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL:
        required: true

defaults:
  run:
    working-directory: ./services/auth_v2

permissions:
  id-token: write
  contents: read

env:
  AWS_ROLE_ARN: arn:aws:iam::638984414044:role/github-actions # GitHub Actions OIDC用IAMロール
  ECR_REGISTRY: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com # ECRレジストリURL
  ECR_REPOSITORY: auth_v2-server

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: setup envs
        run: echo "current_sha=$(git log -1 '--format=format:%H')" >> $GITHUB_ENV

      - uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ap-northeast-1

      - uses: docker/login-action@v2
        with:
          registry: ${{ env.ECR_REGISTRY }}

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          cache: 'gradle'
          distribution: 'adopt'

      # prod, staging, dev1 用のapplication.propertiesを取得
      - name: Get application.properties
        run: |
          aws ssm get-parameter --name PS_AUTH_V2 --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./src/main/resources/application-prod.properties
          aws ssm get-parameter --name PS_STG_AUTH_V2 --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./src/main/resources/application-stg.properties
          aws ssm get-parameter --name PS_DEV1_AUTH_V2 --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./src/main/resources/application-dev1.properties
          aws ssm get-parameter --name PS_DEV2_AUTH_V2 --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./src/main/resources/application-dev2.properties
          aws ssm get-parameter --name PS_DEMO_AUTH_V2 --region ap-northeast-1 --with-decryption --output text --query Parameter.Value > ./src/main/resources/application-demo.properties

      - name: Build
        run: |
          ./gradlew build -x test -x detekt -Dquarkus.profile=prod

      - uses: docker/setup-buildx-action@v2
        with:
          driver-opts: image=moby/buildkit:master

      - uses: docker/login-action@v2
        with:
          registry: ${{ env.ECR_REGISTRY }}

      - uses: docker/build-push-action@v4
        id: push_image
        with:
          push: true
          file: ./services/auth_v2/src/main/docker/Dockerfile.jvm
          context: ./services/auth_v2/
          tags: |
            ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.current_sha || github.sha }}
          # https://docs.docker.com/build/ci/github-actions/cache/#github-cache
          cache-from: type=gha
          cache-to: type=gha,mode=max
          # @see https://github.com/docker/build-push-action/issues/771
          provenance: false

      - name: Notify hash to Slack
        if: success()
        uses: rtCamp/action-slack-notify@master
        env:
          MSG_MINIMAL: true
          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
          SLACK_MSG_AUTHOR: Build report
          SLACK_COLOR: good
          SLACK_MESSAGE: ':+1::+1::+1::+1::+1: Build succeeded on AUTH_V2'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':rocket:'

      - name: Notify hash to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          MSG_MINIMAL: true
          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
          SLACK_MSG_AUTHOR: Build report
          SLACK_COLOR: '#ff0000'
          SLACK_MESSAGE: ':cold_face::cold_face::cold_face::cold_face::cold_face: Build failed on AUTH_V2'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':rocket:'
