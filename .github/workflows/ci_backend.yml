name: __backendテスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

defaults:
  run:
    working-directory: ./services/backend

jobs:
  ci_backend:
    runs-on: ubuntu-latest
    env:
      BUNDLE_WITHOUT: development
      RAILS_ENV: test
      DATABASE_HOSTNAME: 127.0.0.1
      DATABASE_PORT: 3306
      DATABASE_NAME: fanme_api_test
      DATABASE_USERNAME: root
      DATABASE_PASSWORD: password
    if: ${{ inputs.skip != 'true' }}
    services:
      db:
        image: mysql:8.0
        ports:
          - "3306:3306"
        env:
          MYSQL_USERNAME: root
          MYSQL_PASSWORD: password
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: fanme_api_test
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          sudo apt-get update -qq && sudo apt-get install -y --no-install-recommends build-essential ffmpeg nodejs imagemagick default-libmysqlclient-dev

      - name: Install Ruby and gems
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.1.1
          bundler-cache: true
          working-directory: ./services/backend

      - name: Set up database schema
        run: bundle exec rails db:schema:load

      - name: Lint Ruby files
        run: bundle exec rubocop --parallel -c .rubocop.yml

      - name: Run rspec
        run: bundle exec rspec

      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: '#FF0000'
          SLACK_MESSAGE: 'Failed😢 on ${{ github.event.repository.name }}'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':x:'
