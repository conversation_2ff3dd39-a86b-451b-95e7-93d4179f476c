name: Upload to S3

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - "doc/maintenance/**"

permissions:
  id-token: write
  contents: read

env:
  AWS_ROLE_ARN: arn:aws:iam::638984414044:role/github-actions # GitHub Actions OIDC用IAMロール

jobs:
  upload:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Set up AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ap-northeast-1
      - name: Upload to S3
        run: |
          aws s3 cp doc/maintenance/index.html s3://fanme-public-bucket/index.html
