name: __auth_v2テスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

defaults:
  run:
    working-directory: ./services/auth_v2

jobs:
  ci_auth:
    runs-on: ubuntu-latest
    if: ${{ inputs.skip != 'true' }}
    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          cache: 'gradle'
          distribution: 'adopt'

      - name: Detekt
        run: ./gradlew detekt

      - name: Test for Backend
        run: ./gradlew test --info

      - name: Jacoco Report to PR
        id: jacoco
        uses: madrapps/jacoco-report@v1.6.1
        with:
          paths: ./services/auth_v2/build/jacoco-report/jacoco.xml
          token: ${{ secrets.GITHUB_TOKEN }}
          min-coverage-overall: 0
          title: Code Coverage on Auth V2🦑
          update-comment: true

      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: '#FF0000'
          SLACK_MESSAGE: 'Failed😢 on ${{ github.event.repository.name }}'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':x:'

