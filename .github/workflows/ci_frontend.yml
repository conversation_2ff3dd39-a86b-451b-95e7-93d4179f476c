name: __frontendテスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true
      AUTH_TOKEN_FOR_GITHUBPKG:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
  APP_ENV: staging
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  SENTRY_ORG: torihada-inc
  SENTRY_PROJECT: fanme-front

defaults:
  run:
    working-directory: ./services/frontend

jobs:
  ci_frontend:
    runs-on: ubuntu-22.04
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: install canvas dependencies
        run: sudo apt-get install build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev # https://www.npmjs.com/package/canvas

      - uses: actions/setup-node@v3
        with:
          node-version-file: "./services/frontend/.node-version"
          registry-url: "https://npm.pkg.github.com"
          scope: "@torihada-inc"

      - name: Lint and Apply format
        run: npm i yarn -g && yarn --frozen-lockfile && yarn format
        env:
          NODE_AUTH_TOKEN: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

      - name: Build check
        run: yarn build

      - name: Commit & Push changes
        if: ${{ success() }}
        uses: EndBug/add-and-commit@v9
        with:
          author_email: <EMAIL>
          author_name: fanme
          message: Apply formatting rules

      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: "#FF0000"
          SLACK_MESSAGE: "Failed😢 on ${{ github.event.repository.name }}"
          SLACK_USERNAME: "GitHub Actions"
          SLACK_ICON_EMOJI: ":x:"
