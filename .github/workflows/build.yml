name: BUILD

on:
  workflow_dispatch:

jobs:
  build:
    name: ビルド実行
    runs-on: ubuntu-latest
    steps:
      - run: echo "ビルドを開始します"

  call-console-build:
    name: consoleビルド
    needs: build
    uses: ./.github/workflows/build_console.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      AUTH_TOKEN_FOR_GITHUBPKG: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

  call-admin-build:
    name: adminビルド
    needs: build
    uses: ./.github/workflows/build_admin.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}

  call-frontend-build:
    name: frontendビルド
    needs: build
    uses: ./.github/workflows/build_frontend.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      AUTH_TOKEN_FOR_GITHUBPKG: ${{ secrets.AUTH_TOKEN_FOR_GITHUBPKG }}

  call-backend-build:
    name: backendビルド
    needs: build
    uses: ./.github/workflows/build_backend.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}

  call-auth_v2-build:
    name: auth_v2ビルド
    needs: build
    uses: ./.github/workflows/build_auth_v2.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}

  call-payment-build:
    name: paymentビルド
    needs: build
    uses: ./.github/workflows/build_payment.yml
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}

  update-charts:
    name: chartsアップデート
    runs-on: ubuntu-latest
    needs:
      [
        call-console-build,
        call-admin-build,
        call-frontend-build,
        call-backend-build,
        call-payment-build,
        call-auth_v2-build,
      ]
    steps:
      - name: Generate token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.TOKEN_APP_ID }}
          private-key: ${{ secrets.TOKEN_APP_PRIVATE_KEY }}

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.generate_token.outputs.token }}

      - name: Get current_sha
        run: echo "current_sha=${{ github.sha }}" >> $GITHUB_ENV

      - name: Update values.yaml
        uses: mikefarah/yq@master
        with:
          cmd: >
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/auth-serverv2/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-admin/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-api/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-batch/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-front/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/payment-server/values.yaml &&
            yq -i '.image.[].tag = "${{ env.current_sha }}"' ./charts/fanme-console/values.yaml

      - name: Set current datetime
        env:
          TZ: Asia/Tokyo
        run: >
          echo "current_datetime=$(date +'%Y%m%d%H%M%S')" >> $GITHUB_ENV &&
          echo "current_year=$(date +'%Y')" >> $GITHUB_ENV

      - name: Commit and push (main)
        if: ${{ success() && github.ref == 'refs/heads/main' }}
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "main branch build. Applied charts tag (${{ github.sha }})"
          file_pattern: '*.yaml'
          tagging_message: "release/${{ env.current_year }}/${{ env.current_datetime }}"

      - name: Commit and push (other branch)
        if: ${{ success() && github.ref != 'refs/heads/main' }}
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "${{ github.ref_name }} branch build. Applied charts tag (${{ github.sha }})"
          file_pattern: '*.yaml'
          tagging_message: "build/${{ env.current_year }}/${{ env.current_datetime }}"

      - name: Notify hash to Slack
        if: success()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
          SLACK_MSG_AUTHOR: Build report
          SLACK_COLOR: good
          SLACK_MESSAGE: "${{ env.current_sha }}"
          SLACK_USERNAME: "GitHub Actions"
          SLACK_ICON_EMOJI: ":rocket:"

      - name: Notify hash to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.FANME_BUILD_SLACK_WEBHOOK_URL }}
          SLACK_MSG_AUTHOR: Build report
          SLACK_COLOR: "#ff0000"
          SLACK_MESSAGE: "${{ env.current_sha }}"
          SLACK_USERNAME: "GitHub Actions"
          SLACK_ICON_EMOJI: ":pray:"
