name: __paymentテスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

defaults:
  run:
    working-directory: ./services/payment

jobs:
  ci_payment:
    runs-on: ubuntu-latest
    env:
      KTOR_ENV: test
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'adopt'
          cache: 'gradle'
      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@e6e38bacfdf1a337459f332974bb2327a31aaf4b
      - name: Apply formatting rules with Grad<PERSON>
        uses: gradle/gradle-build-action@937999e9cc2425eddc7fd62d1053baf041147db7
        with:
          arguments: ktfmtFormat
          build-root-directory: ./services/payment
      - name: Test with Gradle
        uses: gradle/gradle-build-action@937999e9cc2425eddc7fd62d1053baf041147db7
        with:
          arguments: test
          build-root-directory: ./services/payment
      - name: Generate types
        run: ./gradlew genTypes
      - name: Commit & Push changes
        if: ${{ success() }}
        uses: EndBug/add-and-commit@v9
        with:
          author_email: <EMAIL>
          author_name: formatter
          message: Apply formatting rules to payment
      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: '#FF0000'
          SLACK_MESSAGE: 'Failed😢 on ${{ github.event.repository.name }}'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':x:'
