name: __adminビルド

on:
  workflow_call:
    secrets:
      FANME_BUILD_SLACK_WEBHOOK_URL:
        required: true

defaults:
  run:
    working-directory: ./services/admin

permissions:
  id-token: write
  contents: read

env:
  AWS_ROLE_ARN: arn:aws:iam::638984414044:role/github-actions # GitHub Actions OIDC用IAMロール
  ECR_REGISTRY: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com # ECRレジストリURL
  ECR_REPOSITORY: fanme-admin

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - uses: aws-actions/configure-aws-credentials@v1
        with:
          role-to-assume: ${{ env.AWS_ROLE_ARN }}
          aws-region: ap-northeast-1

      - uses: docker/login-action@v2
        with:
          registry: ${{ env.ECR_REGISTRY }}

      - uses: graalvm/setup-graalvm@v1
        with:
          version: 'latest'
          java-version: '17'
          components: 'native-image'
          cache: 'gradle'

      - name: Build backend
        run: ./gradlew shadowJar

      - name: Build docker image for Backend
        run: |
          docker build -f ./Dockerfile . -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:latest -t ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ github.sha }}
          docker push ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ github.sha }}
          docker push ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:latest

      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: '#FF0000'
          SLACK_MESSAGE: 'Failed😢 on ${{ github.event.repository.name }}'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':x:'
