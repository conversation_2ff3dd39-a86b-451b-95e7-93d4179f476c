name: __adminテスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

defaults:
  run:
    working-directory: ./services/admin

jobs:
  ci_admin:
    runs-on: ubuntu-latest
    env:
      KTOR_ENV: test
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          cache: 'gradle'
          distribution: 'adopt'

      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@v1

      - name: Test with Gradle
        uses: gradle/gradle-build-action@v2
        with:
          arguments: test
          build-root-directory: ./services/admin

      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@master
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: '#FF0000'
          SLACK_MESSAGE: 'Failed😢 on ${{ github.event.repository.name }}'
          SLACK_USERNAME: 'GitHub Actions'
          SLACK_ICON_EMOJI: ':x:'

