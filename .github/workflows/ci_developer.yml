name: __developerテスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}

defaults:
  run:
    working-directory: ./services/developer

jobs:
  ci_developer:
    runs-on: ubuntu-latest
    env:
      KTOR_ENV: test
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
          cache: 'gradle'

      - name: Apply formatting rules with Gradle
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: wrapper
          arguments: ktfmtFormat
          build-root-directory: ./services/developer

      - name: Build with Grad<PERSON>
        uses: gradle/gradle-build-action@v2
        with:
          gradle-version: wrapper
          arguments: test
          build-root-directory: ./services/developer

      - name: Comm<PERSON> & <PERSON>ush changes
        if: ${{ success() }}
        uses: EndBug/add-and-commit@v9
        with:
          author_email: <EMAIL>
          author_name: formatter
          message: Apply formatting rules to developer
