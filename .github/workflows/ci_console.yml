name: __fanme_consoleテスト

on:
  workflow_call:
    inputs:
      skip:
        type: string
    secrets:
      SLACK_ERROR_WEBHOOK_URL:
        required: true

env:
  SLACK_ERROR_WEBHOOK_URL: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
  APP_ENV: staging

defaults:
  run:
    working-directory: ./services/fanme_console

jobs:
  ci_fanme_console:
    runs-on: ubuntu-latest
    if: ${{ inputs.skip != 'true' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version-file: "./services/fanme_console/.node-version"
          registry-url: "https://npm.pkg.github.com"
          scope: "@torihada-inc"

      - name: Lint check
        run: npm i yarn -g && yarn --frozen-lockfile && yarn lint

      - name: Format check
        run: yarn format --check

      - name: Type check
        run: yarn tsc

      - name: Jest Test
        run: yarn test

      - name: Build check
        run: yarn build

      - name: Notify error to Slack
        if: failure()
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_ERROR_WEBHOOK_URL }}
          SLACK_COLOR: "#FF0000"
          SLACK_MESSAGE: "Failed😢 on ${{ github.event.repository.name }}"
          SLACK_USERNAME: "GitHub Actions"
          SLACK_ICON_EMOJI: ":x:"
