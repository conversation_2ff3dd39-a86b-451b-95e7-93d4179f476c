package jp.co.torihada.auth.config

import io.ktor.serialization.kotlinx.json.json
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json

@ExperimentalSerializationApi
fun Application.configureContentNegotiation() {
    install(ContentNegotiation) {
        json(
            Json {
                encodeDefaults = false
                explicitNulls = false
            })
    }
}
