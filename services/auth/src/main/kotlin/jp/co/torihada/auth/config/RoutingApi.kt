package jp.co.torihada.auth.config

import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.AuthenticationStrategy
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import jp.co.torihada.auth.controllers.api.UserVerificationController
import jp.co.torihada.auth.controllers.api.webhook.EkycController

fun Application.configureRoutingApi() {
    routing {
        route("/api/webhook") {
            route("ekyc") {
                // ネクスウエイ側これを受信して認証を行ってるので、うちは受け取れない
                // post("on-result") { EkycController(call).onResult() }
                post("on-api-result") { EkycController(call).onApiResult() }
            }
        }
        authenticate("auth-jwt-with-user-token-fallback", strategy = AuthenticationStrategy.Required) {
            route("/api") {
                route("user") {
                    route("verification") {
                        get("stat") { UserVerificationController(call).getStat() }
                        post("issue-url") { UserVerificationController(call).issueUrl() }
                        post("apply") { UserVerificationController(call).apply() }
                    }
                }
            }
        }
        authenticate("auth-basic-api") {
            route("/api") { route("user") { route("verification") { post("update") { UserVerificationController(call).update() } } } }
        }
    }
}
