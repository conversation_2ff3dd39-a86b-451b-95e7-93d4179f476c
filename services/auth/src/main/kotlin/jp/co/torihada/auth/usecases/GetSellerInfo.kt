package jp.co.torihada.auth.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jp.co.torihada.auth.entities.CustomError
import jp.co.torihada.auth.entities.Errors
import jp.co.torihada.auth.models.AppUserTokens
import jp.co.torihada.auth.models.User
import jp.co.torihada.auth.models.Users

object GetSellerInfo {
    class Input(val uuid: String)

    class Output(val user: User)

    fun execute(params: Input): Result<Output, CustomError> {
        // TODO: 本来はclient_idをもらってアプリを識別し、そのアプリ内のuuidで検索すること
        val appUserToken = AppUserTokens.findByUuid(params.uuid) ?: return Err(Errors.TOKEN_INVALID_ID_TOKEN.value)
        val user = Users.findById(appUserToken.userId) ?: return Err(Errors.TOKEN_USER_NOT_EXIST.value)

        return Ok(Output(user))
    }
}
