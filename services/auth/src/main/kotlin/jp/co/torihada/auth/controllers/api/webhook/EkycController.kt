package jp.co.torihada.auth.controllers.api.webhook

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.receiveText
import jp.co.torihada.auth.Env
import jp.co.torihada.auth.controllers.BaseController
import jp.co.torihada.auth.entities.WellKnownError
import jp.co.torihada.auth.entities.request.webhook.EkycWebhookBpoResultRequest
import jp.co.torihada.auth.entities.request.webhook.EkycWebhookResultRequest
import jp.co.torihada.auth.extensions.idValue
import jp.co.torihada.auth.extensions.respondError
import jp.co.torihada.auth.libraries.vendor.Ekyc
import jp.co.torihada.auth.models.AppUserTokens
import jp.co.torihada.auth.models.UserVerification
import jp.co.torihada.auth.models.UserVerifications
import jp.co.torihada.auth.models.Users
import jp.co.torihada.auth.services.EmailService
import jp.co.torihada.auth.usecases.user.GetVerificationStatus
import jp.co.torihada.auth.usecases.vendor.ekyc.GetResult
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.transactions.transaction

class EkycController(call: ApplicationCall) : BaseController(call) {
    suspend fun onResult() {
        return handleRequest<EkycWebhookResultRequest> {
            val verification =
                UserVerifications.findByReferenceId(request.applyNo) ?: return call.respondError(WellKnownError("verification not found"))

            val user = Users.findById(verification.userId) ?: return call.respondError(WellKnownError("user not found"))

            transaction {
                verification.status = UserVerification.Status.SUBMITTED.value
                verification.metadata =
                    Json.encodeToString(UserVerification.UserVerificationMetadata(verifyUrl = verification.redirectUrl, error = null))
            }

            // 受付完了
            EmailService.sendUserVerificationAccepted(user.contactEmail!!, user, verification)

            return Ekyc.respondOk(call)
        }
    }

    suspend fun onApiResult() {
        return handleRequest<EkycWebhookBpoResultRequest> {
            if (!Env.isTest) {
                Ekyc.verifySignByRequest(call.request, call.receiveText())
            }
            val verification =
                UserVerifications.findByApplicantId(request.applicantId)
                    ?: return call.respondError(WellKnownError("verification not found"))

            val user = Users.findById(verification.userId) ?: return call.respondError(WellKnownError("user not found"))

            if (Env.isTest) {
                UserVerifications.completeUserVerification(user.idValue, matched = request.isMatch, result = null)
            } else {
                when (val r = GetResult.execute(GetResult.Input(http, verification.applicantId, false))) {
                    is Ok -> {
                        val userUuid =
                            AppUserTokens.findByUserId(1, user.idValue)?.uuid
                                ?: return call.respondError(WellKnownError("user uuid not found"))

                        GetVerificationStatus.notificationRegister(r.value.matched, userUuid)
                        UserVerifications.completeUserVerification(user.idValue, r.value.matched, r.value.raw)
                    }
                    is Err -> return Ekyc.bpoRespondFailed(call)
                }
            }

            // メール通知
            EmailService.sendUserVerificationResult(user.contactEmail!!, user, verification)
            return Ekyc.bpoRespondOk(call)
        }
    }
}
