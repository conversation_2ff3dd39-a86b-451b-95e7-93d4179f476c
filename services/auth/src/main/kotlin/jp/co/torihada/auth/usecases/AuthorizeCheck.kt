package jp.co.torihada.auth.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jp.co.torihada.auth.entities.CustomError
import jp.co.torihada.auth.entities.Errors
import jp.co.torihada.auth.models.AppUserTokens
import jp.co.torihada.auth.models.Apps
import jp.co.torihada.auth.models.AuthorizationScope
import jp.co.torihada.auth.models.User

// TODO: 一旦コードを移しただけなのでリファクタリング必要
object AuthorizeChecker {

    class Input(
        val responseType: String,
        val clientId: String,
        val redirectUri: String,
        val state: String,
        val scope: String,
        val nonce: String?,
        val prompt: String?,
        val maxAge: String?,
        val uiLocales: String?,
        val disableAutoLogin: Boolean,
        val codeChallenge: String?,
        val codeChallengeMethod: String?,
        val returnUrl: String?,
        val user: User?
    )

    class Output(val userScopePermission: UserScopePermission)

    fun execute(params: Input): Result<Output, CustomError> {
        // client_id からレコードを取得し、リクエスト内容が正しいかをチェック
        val app = Apps.findByClientId(params.clientId) ?: return Err(Errors.AUTH_INVALID_CLIENT_ID.value)

        // redirectUrlがマッチしているかを確認
        if (!app.domainMatches(params.redirectUri)) {
            return Err(Errors.AUTH_INVALID_REDIRECT_URI.value)
        }

        // パラメータチェックしてから、ログイン状態を調べて、必要ならリダイレクト
        if (params.user == null) {
            return Err(Errors.AUTH_NOT_SIGNED_IN.value)
        }

        // 許可パラメータの状態を設定
        val permission =
            UserScopePermission(
                showProfilePermission = params.scope.contains(AuthorizationScope.Profile.scope),
                showEmailPermission = params.scope.contains(AuthorizationScope.Email.scope),
                profilePermitted = false,
                emailPermitted = false)

        // すでにトークンが発行されているかを確認し、その値を反映
        val user = AppUserTokens.findByUserId(app.id.value, params.user.id.value)
        if (user != null) {
            permission.profilePermitted = permission.showProfilePermission && user.scopes.contains(AuthorizationScope.Profile.scope)
            permission.emailPermitted = permission.showEmailPermission && user.scopes.contains(AuthorizationScope.Email.scope)
        }
        return Ok(Output(userScopePermission = permission))
    }
}

data class UserScopePermission(
    var showProfilePermission: Boolean,
    var showEmailPermission: Boolean,
    var profilePermitted: Boolean,
    var emailPermitted: Boolean
)
