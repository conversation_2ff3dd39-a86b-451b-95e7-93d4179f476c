package jp.co.torihada.auth.controllers

import com.auth0.jwt.interfaces.Claim
import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import io.ktor.http.*
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.*
import io.ktor.server.response.respond
import jp.co.torihada.auth.entities.request.user.*
import jp.co.torihada.auth.entities.response.user.*
import jp.co.torihada.auth.usecases.*
import jp.co.torihada.auth.usecases.users.DeleteUser

class UserController(call: ApplicationCall) : BaseController(call) {
    /*
     * ユーザー情報を取得
     */
    suspend fun get() {
        val params =
            when (val r = parseParams<UserInfoRequest>()) {
                is Ok -> r.value
                is Err -> return renderBadRequest(r.error)
            }

        val principal = getPrincipal()

        val uuid = getUuid(principal) ?: return call.respond(HttpStatusCode.Unauthorized, "user not found")

        var payloadProvider = (principal?.payload?.getClaim("provider") as Claim).asString()
        if (payloadProvider == null) {
            payloadProvider = "email"
        }

        when (val r = UserVerify.execute(params.toUserVerifyInput(uuid))) {
            is Ok -> {
                call.respond(
                    UserInfoResponse(
                        name = r.value.user.name,
                        email = r.value.user.contactEmail,
                        loginEmail = r.value.user.loginEmail,
                        externalAuth = r.value.user.isExternalAuthUser,
                        externalAuthType = payloadProvider,
                    ))
            }
            is Err -> call.respond(UnauthorizedResponse())
        }
    }

    /*
     * ユーザー名の更新
     */
    suspend fun changeName() {
        val params =
            when (val r = parseParams<ChangeUserNameRequest>()) {
                is Ok -> r.value
                is Err -> return renderBadRequest(r.error)
            }

        val uuid = getUuid() ?: return call.respond(HttpStatusCode.Unauthorized, "user not found")

        when (val r = ChangeUserName.execute(params.toChangeUserNameInput(uuid))) {
            is Ok -> {
                call.respond(
                    ChangeUserNameResponse(
                        name = r.value.user.name,
                        email = r.value.user.contactEmail,
                    ))
            }
            is Err -> call.respond(UnauthorizedResponse())
        }
    }

    suspend fun changeUserInfo() {
        val params =
            when (val r = parseParams<ChangeUserInfoRequest>()) {
                is Ok -> r.value
                is Err -> return renderBadRequest(r.error)
            }

        val uuid = getUuid() ?: return call.respond(HttpStatusCode.Unauthorized, "user not found")

        when (val r = ChangeUserInfo.execute(params.toChangeUserInfoInput(uuid))) {
            is Ok -> {
                call.respond(
                    ChangeUserInfoResponse(
                        name = r.value.user.name,
                        gender = r.value.user.gender,
                        birthday = r.value.user.birthday,
                        birthdayConfirmed = r.value.user.birthdayConfirmed,
                    ))
            }
            is Err -> call.respond(UnauthorizedResponse())
        }
    }

    suspend fun getSellerInfo() {
        val params =
            when (val r = parseParams<GetSellerInfoRequest>()) {
                is Ok -> r.value
                is Err -> return renderBadRequest(r.error)
            }
        when (val r = GetSellerInfo.execute(GetSellerInfo.Input(params.uuid!!))) {
            is Ok -> {
                call.respond(
                    GetSellerInfoResponse(
                        name = r.value.user.name,
                        email = r.value.user.contactEmail,
                    ))
            }
            is Err -> call.respond(UnauthorizedResponse())
        }
    }

    suspend fun delete() {
        val uuid = getUuid() ?: return call.respond(HttpStatusCode.Unauthorized, "user not found")

        when (val r = DeleteUser.execute(DeleteUser.Input(uuid))) {
            is Ok -> call.respond(HttpStatusCode.OK)
            is Err -> call.respond(r.error)
        }
    }
}
