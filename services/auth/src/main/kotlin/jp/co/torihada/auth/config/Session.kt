package jp.co.torihada.auth.config

import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.sessions.SessionTransportTransformerEncrypt
import io.ktor.server.sessions.Sessions
import io.ktor.server.sessions.cookie
import io.ktor.util.hex
import java.security.SecureRandom
import jp.co.torihada.auth.Env
import jp.co.torihada.auth.entities.session.AuthorizeSession
import jp.co.torihada.auth.entities.session.UserSession
import kotlin.collections.set

fun Application.configureSession() {
    install(Sessions) {
        // 最初の32Bytesのみを使う
        val secretKey = hex(Env.get("SECRET_BASE")).sliceArray(IntRange(0, 31))
        val transformer =
            SessionTransportTransformerEncrypt(
                secretKey,
                secretKey,
                ivGenerator = { _ -> ByteArray(16).apply { SecureRandom().nextBytes(this) } },
            )

        cookie<UserSession>("s") {
            cookie.path = "/"
            if (Env.isProd || Env.isStg || Env.isDev1) {
                cookie.secure = true
                cookie.extensions["SameSite"] = "lax"
            }
            transform(transformer)
        }

        cookie<AuthorizeSession>("a") {
            cookie.path = "/"
            if (Env.isProd || Env.isStg || Env.isDev1) {
                cookie.secure = true
                cookie.extensions["SameSite"] = "lax"
            }
            transform(transformer)
        }
    }
}
