package jp.co.torihada.auth.config

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.http.*
import io.ktor.http.auth.parseAuthorizationHeader
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.auth.Authentication
import io.ktor.server.auth.OAuthServerSettings
import io.ktor.server.auth.UserIdPrincipal
import io.ktor.server.auth.basic
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.auth.oauth
import io.ktor.server.request.authorization
import io.ktor.server.request.header
import io.ktor.server.sessions.*
import io.ktor.util.*
import jp.co.torihada.auth.Const.attributeKeyUserToken
import jp.co.torihada.auth.Const.headerFanmeUserToken
import jp.co.torihada.auth.Env
import jp.co.torihada.auth.Security
import jp.co.torihada.auth.entities.session.AuthorizeSession

fun Application.configureAuthentication() {
    install(Authentication) {
        val httpClient = HttpClient(CIO)

        val jwkIssuer = Env.get("JWT_ISSUER")

        val algorithm = Algorithm.RSA256(Security.publicKey, Security.privateKey)

        val verifier = JWT.require(algorithm).withIssuer(jwkIssuer).build()
        jwt("auth-jwt") {
            verifier(verifier)
            validate { credential ->
                if (credential.payload.subject != "") {
                    JWTPrincipal(credential.payload)
                } else {
                    null
                }
            }
        }
        jwt("auth-jwt-with-user-token-fallback") {
            authHeader {
                if (!it.request.authorization().isNullOrBlank()) {
                    val h = it.request.authorization()!!
                    if (h.lowercase().startsWith("basic")) {
                        val uh = it.request.header(headerFanmeUserToken) ?: return@authHeader null
                        it.attributes.put(AttributeKey(attributeKeyUserToken), uh)
                        parseAuthorizationHeader("Bearer $uh")
                    } else {
                        var token = h
                        if (token.substring(0, 6).lowercase() == "bearer") {
                            token = token.substring("bearer".length).trim()
                        }
                        it.attributes.put(AttributeKey(attributeKeyUserToken), token)
                        parseAuthorizationHeader(h)
                    }
                } else {
                    null
                }
            }
            verifier(verifier)
            validate { credential ->
                if (credential.payload.subject != "") {
                    JWTPrincipal(credential.payload)
                } else {
                    null
                }
            }
        }

        oauth("line") {
            urlProvider = {
                val call = this
                val defaultCallbackUrl = Env.getOauthCallbackUrl("line")

                // ①call.sessions から取得しているのは、
                // fanme-apiのリダイレクトから /authorize 飛ばされた際に保存されている戻り先パラメータ
                // これを、LINEの認証URLリダイレクト時のパラメータとして、
                // redirect_uri=http://localhost:8080/signin/callback/line?wb_return_url=〇〇
                // として渡す。
                //
                // ②call.parametersで設定しているのは、
                // ①でLINEにリダイレクトされて、LINEログイン後に /signin/callback/line に飛ばされた際の①からの引き継ぎ
                // パラメータ(wb_return_url)を再度設定する。
                // これはaccessTokenを取得する処理で、urlProviderの値を redirect_uri としてLINEに渡すため、
                // wb_return_url の値が存在する場合に、それを渡さないと、別の redirect_uri になって
                // しまい、accessTokenが不正に取得されていると判断されてしまう。
                val returnUrl = call.sessions.get<AuthorizeSession>()?.authorizeRequest?.returnUrl ?: call.parameters["wb_return_url"]

                // LINEの認証画面に渡すredirect_uriに、FANME画面に戻るためのパラメータ(wb_return_url)を付与する
                val redirectUri =
                    if (returnUrl != null) {
                        val urlBuilder = URLBuilder(defaultCallbackUrl)
                        urlBuilder.parameters.append("wb_return_url", returnUrl)
                        urlBuilder.buildString()
                    } else {
                        defaultCallbackUrl
                    }

                redirectUri
            }
            providerLookup = {
                OAuthServerSettings.OAuth2ServerSettings(
                    name = "line",
                    authorizeUrl = "https://access.line.me/oauth2/v2.1/authorize?bot_prompt=aggressive",
                    accessTokenUrl = "https://api.line.me/oauth2/v2.1/token",
                    requestMethod = HttpMethod.Post,
                    clientId = Env.get("OAUTH_LINE_CLIENT_ID"),
                    clientSecret = Env.get("OAUTH_LINE_CLIENT_SECRET"),
                    defaultScopes = listOf("openid email profile"))
            }
            client = httpClient
        }

        oauth("twitter") {
            urlProvider = { Env.getOauthCallbackUrl("twitter") }
            providerLookup = {
                OAuthServerSettings.OAuth2ServerSettings(
                    name = "twitter",
                    authorizeUrl = "https://twitter.com/i/oauth2/authorize" + "?code_challenge=challenge&code_challenge_method=plain",
                    accessTokenUrl = "https://api.twitter.com/2/oauth2/token?code_verifier=challenge",
                    requestMethod = HttpMethod.Post,
                    clientId = Env.get("OAUTH_TWITTER_CLIENT_ID"),
                    clientSecret = Env.get("OAUTH_TWITTER_CLIENT_SECRET"),
                    defaultScopes = listOf("users.read tweet.read offline.access"))
            }
            client = httpClient
        }

        oauth("instagram") {
            urlProvider = { Env.getOauthCallbackUrl("instagram") }
            providerLookup = {
                OAuthServerSettings.OAuth2ServerSettings(
                    name = "google",
                    authorizeUrl = "https://api.instagram.com/oauth/authorize",
                    accessTokenUrl = "https://api.instagram.com/oauth/access_token",
                    requestMethod = HttpMethod.Post,
                    clientId = Env.get("OAUTH_INSTAGRAM_CLIENT_ID"),
                    clientSecret = Env.get("OAUTH_INSTAGRAM_CLIENT_SECRET"),
                    defaultScopes = listOf("user_profile"))
            }
            client = httpClient
        }

        oauth("google") {
            urlProvider = { Env.getOauthCallbackUrl("google") }
            providerLookup = {
                OAuthServerSettings.OAuth2ServerSettings(
                    name = "google",
                    authorizeUrl = "https://accounts.google.com/o/oauth2/auth",
                    accessTokenUrl = "https://accounts.google.com/o/oauth2/token",
                    requestMethod = HttpMethod.Post,
                    clientId = Env.get("OAUTH_GOOGLE_CLIENT_ID"),
                    clientSecret = Env.get("OAUTH_GOOGLE_CLIENT_SECRET"),
                    defaultScopes =
                        listOf(
                            "openid " +
                                "email " +
                                "profile " +
                                // "https://www.googleapis.com/auth/youtube " +
                                "https://www.googleapis.com/auth/userinfo.profile"),
                    authorizeUrlInterceptor = { this.parameters.append("access_type", "offline") })
            }
            client = httpClient
        }

        basic("auth-basic-api") {
            realm = "Access to the '/' path"
            validate { credentials ->
                if (credentials.name == Env.authApiName && credentials.password == Env.authApiPassword) {
                    UserIdPrincipal(credentials.name)
                } else {
                    null
                }
            }
        }
    }
}
