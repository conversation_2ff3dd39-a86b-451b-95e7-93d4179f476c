PRIVATE_KEY=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCec/XA757wnGIs45gfhG8VNvEmE5Xu2qgYw2fkU7Ar+fzbRgHxz33lIZ0HPLNJqIjOPKKr+RWHnuPBIw9PUa+YqDsDsT39Z4Tn42SGAZOwVQVGkOjaZaAm+PszSdlLBOGA++Ethd++bLMvarvbCl2S35lfRZpN/K/z/3cCdD/T/EhHvvkDLfj9Zn8T/NNLSK9taWu/neP1gArWsqCE2U6GCj+oGjuPlBKydWg4H3gC7Ddw8L8AqO+x2UKkg2gbyXx98V4Dq7viupL3aGG95wtFUdzZDYAd8mrZ/hEv2u1YMKWqODsl8CiFB9RXrGopa9rwzfY0+8Ip2IGUT0wfLVATAgMBAAECggEAK4FXNnQURagKZJzQxcaM4VxGddfm0MAEa1grjEtWEZz/ygy8t/HqdSD3hmcqKs01UrrfRyyKtsPvcLX5nCOnfNij622cwZjMqwaKMSj5re6KFkfvRxekE09FWV2rZaefatS9jMw3FIev4xMBJTEBZDQ0Xi40IbmBbhL0UB4u8Drj2qhBiffl5u2cKAp1WVVh4wA5/915g0vaa5ozD5+KL2Qe0/17cULvaCW4t0IMJ+f5RCzn2I6R4KD83XBje7hUH/DfyhNmkb+77DB7YbWuvG3daHgiNPyN/gTAwLXU56X63hLv6924Rh2nxw17VNlmPS2WFWZnPUBoO3JlaHEdgQKBgQDLtYiVMzLkLZZEv5TZpB5Jroag3LWTQivRFFz1vp0QFGMU5KQ9lhq+hOU3GePm5FF6vRMflWoZ7LvMcHMEDoFPyLB4BZcZ9WE3pBeZUyJsZrzrW0tBdZUtRF4J3GhLK5Q7oRHrLlmPQt2OGUNzl4NO/ZZy4KmFN0RlecM/0Odq4QKBgQDHIHsEmjX7lv9yKUoqBxLKsdo2sm3xmLSEizXnL0APOcPfupt5VxT9olm/Sdd5awekX1Oe8V8RMXSRADtJRnLUTD6KYsMaU+vQVD+wK+03zsgFO8fXLul53sUB+/jbLMTllPmcfy0le1SAZL98cOQ0yZdbOUh4IvvU6f7niAbtcwKBgGpYMZ5aJRYzqj/nl+hqohkrEENIFU5z+/XhYIo14T60tekBV7CC/SE6tYKndG9kABlIPYR+du5jxMJ8bb7PZ7hj96QgnvpBPGhUtLEpUk4u8C8Z8NaScLc5+rHBK30amf7rHjSddMmySYFWNP1SOo1xGyqLmFPgP0eOYlydTUJhAoGAJz/5v6B9/4d19pS476vRQm7oKlvlW/fTrUSbbWRc5iNmvpTTzIkjtBLqSdBOTrqQGRBJNU0yhfsPlP1MslbWj44vzatF9cyQ8NGKdDlUWunBd3afH4U5tDH69zcpiFo8vVwuD3Dp/zdeIqi5Ldg6w0TxNqQF6thKd19gRVcEto0CgYAgj7h8IEr/7a4w0+Kn4TQlBjevscOATz+//6ytvyP1yscHRZ/8A2BXaBE9ckqnPoKhbcgV5shbElKwhte7A/FZf+cJz/xdaOwEoMBJ+fr0mXjjF7IYSVm/vNXqmPM6tdkNYOcb4legWrV9OUY1mu4bkqTnbH0J4PzeR6sLpJWu7A==
PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnnP1wO+e8JxiLOOYH4RvFTbxJhOV7tqoGMNn5FOwK/n820YB8c995SGdBzyzSaiIzjyiq/kVh57jwSMPT1GvmKg7A7E9/WeE5+NkhgGTsFUFRpDo2mWgJvj7M0nZSwThgPvhLYXfvmyzL2q72wpdkt+ZX0WaTfyv8/93AnQ/0/xIR775Ay34/WZ/E/zTS0ivbWlrv53j9YAK1rKghNlOhgo/qBo7j5QSsnVoOB94Auw3cPC/AKjvsdlCpINoG8l8ffFeA6u74rqS92hhvecLRVHc2Q2AHfJq2f4RL9rtWDClqjg7JfAohQfUV6xqKWva8M32NPvCKdiBlE9MHy1QEwIDAQAB

GMO_SITE_ID=tsite00046822
GMO_SITE_PASS=m3xvyp6g

GMO_SHOP_ID=rshop00000567
GMO_SHOP_PASS=t8k8x3cy
GMO_API_URL_DEPOSIT_SEARCH=https://test-remittance.gmopg.jp/api/shop/DepositSearch.json
GMO_API_URL_DEPOSIT_REDIRECTION=https://test-remittance.gmopg.jp/api/DepositRegistration.json
GMO_API_URL_LINK_REDIRECT_URL=https://test-remittance.gmopg.jp/api/shop/LinkRedirectUrl.json

TENANT=fanme

PAYMENT_DB_URL="***************************************"
PAYMENT_DB_NAME=payment
PAYMENT_DB_USER=root
PAYMENT_DB_PASS=pass

AUTH_DB_URL="***************************************"
AUTH_DB_NAME=auth
AUTH_DB_USER=root
AUTH_DB_PASS=pass

FANME_API_DB_URL="***************************************"
FANME_API_DB_NAME=fanme_api
FANME_API_DB_USER=root
FANME_API_DB_PASS=password

MONGO_URL="*******************************"
AWS_ACCESS_KEY_ID=test_key
AWS_SECRET_ACCESS_KEY=test_key
LOCAL_S3_ENDPOINT="http://minio:9000"
LOCAL_S3_URL="http://127.0.0.1:23003"

MINIAPP_BUCKET="miniapp"
MINIAPP_BLOB_BUCKET="miniapp-blob"

PAYMENT_HOST=
MINIAPP_HOST=

JWK_PROVIDER="http://host.docker.internal:22000/oauth2/certs"
JWT_ISSUER="http://host.docker.internal:22000"

EMAIL_FROM=<EMAIL>
EMAIL_SMTP_HOST=mailhog
EMAIL_SMTP_PORT=1025
EMAIL_SMTP_USER=test
EMAIL_SMTP_PASS=test

TENANT_MAIL_SENDER_ADDRESS_FANME=<EMAIL>
TENANT_MAIL_SENDER_NAME_FANME=FANME

TENANT_MANAGER_EMAIL_FANME=<EMAIL>
TENANT_MANAGER_GMO_AUTH_CODE_FANME=fanme123

GOOGLE_CLIENT_ID=896475418310-kisakh4lmpje5b4shslmkvnturj89kbr.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:24000/login
