import React from 'react'
import Image from 'next/image'
import styled from '@emotion/styled'
import { keyframes } from '@emotion/react'
import BannerBackgroundPC from '@/assets/img/lp/fanmelpbannerPC.webp'
import BannerBackgroundSP from '@/assets/img/lp/fanmelpbannerSP.webp'
import { Colors, Gradation } from '@/constants/styles/color'

import type { NextPage } from 'next'

type props = {
  className?: string
}

// Scroll animation keyframes
const scrollEffect = keyframes`
  60% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
  }
`

// Custom scroll bar for SP version
const StyledSPScrollWrapper = styled.div`
  position: absolute;
  right: 16px;
  bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
`

const StyledSPScrollText = styled.div`
  font-family: 'DM Sans', sans-serif;
  font-size: 10px;
  font-weight: 600;
  line-height: 10px;
  letter-spacing: 0.15em;
  color: ${Colors.PRIMARY_GRAY};
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: rotate(-90deg);
  margin-bottom: 24px;
`

const StyledSPScrollBarContainer = styled.div`
  position: relative;
  width: 1px;
  height: 80px;
  overflow: hidden;
`

const StyledSPScrollBarBackground = styled.div`
  width: 1px;
  height: 80px;
  background: linear-gradient(to bottom, #dedede 50%, ${Colors.PRIMARY_GRAY} 50%);
`

const StyledSPScrollBarAnimated = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 80px;
  background: ${Colors.PRIMARY_GRAY};
  -webkit-animation: ${scrollEffect} 2.5s infinite;
  animation: ${scrollEffect} 2.5s infinite;
  -webkit-animation-delay: 1s;
  animation-delay: 1s;
  -webkit-transform: translateY(-100%);
  transform: translateY(-100%);
`

const Banner: NextPage<props> = ({ className = '' }) => {
  return (
    <>
      {/* PC Version */}
      <section
        className={`hidden sm:block relative w-full h-[720px] ${className}`}
        style={{ aspectRatio: '1360/720' }}
      >
        <Image
          src={BannerBackgroundPC}
          alt="Background"
          fill
          style={{ objectFit: 'cover' }}
          quality={100}
        />
        <div className="absolute inset-0 flex flex-col justify-center items-start px-16">
          <div className="max-w-[600px]">
            <Image src="/Logo.svg" alt="FANME" width={250} height={56} className="mb-8" />
            <h1
              className="text-[48px] font-bold leading-[72px] mb-6"
              style={{
                background: Gradation.LOGO_Clr2_45,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              満たされた毎日を
              <br />
              ファンと一緒につくる
            </h1>
            <p className="text-[18px] leading-[27px]" style={{ color: Colors.PRIMARY_GRAY }}>
              あなたの活動や世界観を丸ごと形にできる。
              <br />
              ファンが広がるクリエイター活動まとめサービス。
            </p>
          </div>
        </div>
      </section>

      {/* SP Version */}
      <section
        className={`block sm:hidden relative w-full h-[479px] ${className}`}
        style={{ aspectRatio: '375/479' }}
      >
        <Image
          src={BannerBackgroundSP}
          alt="Background"
          fill
          style={{ objectFit: 'cover' }}
          quality={100}
        />
        <div className="absolute inset-0 flex flex-col justify-center items-start px-6">
          <div className="max-w-[300px]">
            <Image src="/Logo.svg" alt="FANME" width={116} height={26} className="mb-6" />
            <h1
              className="text-[24px] font-bold leading-[36px] mb-4"
              style={{
                background: Gradation.LOGO_Clr2_45,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
              }}
            >
              満たされた毎日を
              <br />
              ファンと一緒につくる
            </h1>
            <p className="text-[14px] leading-[21px]" style={{ color: Colors.PRIMARY_GRAY }}>
              あなたの活動や世界観を丸ごと形にで
              <br />
              きる。ファンが広がるクリエイター活
              <br />
              動まとめサービス。
            </p>
          </div>
        </div>

        {/* SP Scroll Indicator */}
        <StyledSPScrollWrapper>
          <StyledSPScrollText>SCROLL</StyledSPScrollText>
          <StyledSPScrollBarContainer>
            <StyledSPScrollBarBackground />
            <StyledSPScrollBarAnimated />
          </StyledSPScrollBarContainer>
        </StyledSPScrollWrapper>
      </section>
    </>
  )
}

export default Banner
